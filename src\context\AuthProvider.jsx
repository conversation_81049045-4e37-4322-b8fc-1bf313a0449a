import { createContext, useEffect, useState } from 'react'
import PropTypes from 'prop-types'
import { getLocalStroage } from '../utils/LocalStrorage'

export const AuthContext = createContext()

const AuthProvider = ({children}) => {

  const[userData,setUserData] = useState(null)

  useEffect(()=>{
    const {employees} = getLocalStroage()
    setUserData(employees)
  },[])

  return (
    <div>
      <AuthContext.Provider value={[userData, setUserData]}>
        {children}
      </AuthContext.Provider>
    </div>
  )
}

AuthProvider.propTypes = {
  children: PropTypes.node.isRequired
}

export default AuthProvider
