
import PropTypes from 'prop-types'
import Header from '../others/Header'
import CreateTask from '../others/CreateTask'
import AllTask from '../others/AllTask'

const AdminDashboard = ({changeUser}) => {
  return (
    <div className='h-screen w-full p-10'>
      <Header changeUser={changeUser} />
      <CreateTask/>
      <AllTask/>
    </div>
  )
}

AdminDashboard.propTypes = {
  changeUser: PropTypes.func.isRequired
}

export default AdminDashboard
