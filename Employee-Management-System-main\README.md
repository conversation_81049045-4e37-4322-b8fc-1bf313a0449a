
# Employee Management System

I have made a compact yet feature-rich employee management system where admins can create tasks, assign them to users, and monitor their status, including completed, pending, or failed. Users can view assigned tasks, accept or forward them, and update their progress by marking tasks as completed or failed, ensuring streamlined task tracking and accountability.

## 🛠 Skills
Javascript, React, Context Api, Tailwind css and Local Storage handling...
## Run Locally

Clone the project

```bash
  https://github.com/malhotraarshdeepsingh/Employee-Management-System
```

Go to the project directory

```bash
  cd Employee-Management-System
```

Install dependencies

```bash
  npm install
```

Start the server

```bash
  npm run dev
```


## Demo Videos

You can find login credentials in localStorage.jsx to test the project.

[Create Task](./demo/CreateTask.mp4)
[Forward Task](./demo/ForwardTask.mp4)
[Change Task Status](./demo/StausUpdate.mp4)

## Features
- Admin and user roles with distinct privileges
- Task assignment, forwarding, and status updates
- Real-time task tracking and filtering (completed, pending, or failed)
## Contributing

Feel free to fork the repository, create a new branch, and submit a pull request for review.
